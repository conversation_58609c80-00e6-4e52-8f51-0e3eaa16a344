/**
 * Log messages - English
 */
export default {
  reportController: {
    getCommonReportListEntry: "getCommonReportList entry parameters",
    getCommonReportListReturn: "getCommonReportList return",
    getCommonReportListError: "getCommonReportList exception",
    cancelUploadStart: "Cancel wave file upload start",
    cancelUploadError: "Cancel wave file upload exception",
    openWaveFileStart: "Open wave file start",
    openWaveFileError: "Open wave file exception",
    getGroupReportStart: "Get group report start",
    getGroupReportError: "Get group report exception",
    getOperateReportStart: "Get operate report start",
    getOperateReportError: "Get operate report exception",
    getAuditReportStart: "Get audit report start",
    getAuditReportError: "Get audit report exception",
    exportCommonReportStart: "Export common report start",
    exportCommonReportError: "Export common report exception",
    clearReportStart: "Clear report start",
    clearReportError: "Clear report exception",
    refreshReportStart: "Refresh common report start",
    refreshReportError: "Refresh common report exception",
    refreshGroupReportStart: "Refresh group report start",
    refreshGroupReportError: "Refresh group report exception",
    refreshOperateReportStart: "Refresh operate report start",
    refreshOperateReportError: "Refresh operate report exception",
    refreshTripReportStart: "Refresh trip report start",
    refreshTripReportError: "Refresh trip report exception",
    uploadWaveStart: "Wave file upload start",
    uploadWaveError: "Wave file upload exception"
  },
  configureService: {
    getConfigureListError: "Get configuration list exception",
    loadConfigureError: "Load configuration exception"
  },
  // Controller logs
  configureController: {
    initialized: "Controller initialized",
    getConfigureListStart: "Start getting configuration list",
    getConfigureListError: "Error occurred while getting configuration list",
    addConfigureStart: "Start adding configuration",
    addConfigureError: "Error occurred while adding configuration",
    renameConfigureStart: "Start renaming configuration",
    renameConfigureError: "Error occurred while renaming configuration",
    removeConfigureStart: "Start removing configuration",
    removeConfigureError: "Error occurred while removing configuration",
    saveConfigureStart: "Start saving configuration",
    saveConfigureError: "Error occurred while saving configuration",
    loadConfigureStart: "Start loading configuration",
    loadConfigureError: "Error occurred while loading configuration",
    openConfigureDirStart: "Start opening configuration directory",
    openConfigureDirError: "Error occurred while opening configuration directory"
  },
  deviceConnectController: {
    initialized: "Controller initialized",
    connectDeviceStart: "Start connecting device, connection parameters",
    connectDeviceCallService: "Call service layer to connect device",
    connectDeviceServiceResult: "Service layer return result",
    connectDeviceSuccess: "Device connection successful",
    connectDeviceGetError: "Get error information, log output",
    connectDeviceFailed: "Device connection failed, error information",
    connectDeviceException: "Catch exception, log output",
    connectDeviceExceptionDetail: "Device connection exception",
    disconnectDeviceStart: "Start disconnecting device, device ID",
    disconnectDeviceCheckStatus: "Check device connection status",
    disconnectDeviceAlready: "Device already disconnected, device ID",
    disconnectDeviceCallService: "Call service layer to disconnect device",
    disconnectDeviceResult: "Disconnect result",
    disconnectDeviceSuccess: "Device disconnection successful, device ID",
    disconnectDeviceException: "Device disconnection exception, device ID",
    disconnectDeviceFailed: "Device disconnection failed, device ID"
  },
  deviceOperateController: {
    initialized: "Controller initialized",
    addDeviceStart: "Start adding device configuration, request parameters",
    addDeviceCallService: "Call service layer to add device configuration",
    addDeviceSuccess: "Add device configuration successful, result",
    addDeviceException: "Add device configuration exception",
    updateDeviceStart: "Start updating device configuration, request parameters",
    updateDeviceCallService: "Call service layer to update device configuration",
    updateDeviceSuccess: "Update device configuration successful, result",
    updateDeviceException: "Update device configuration exception",
    removeDeviceStart: "Start removing device configuration, request parameters",
    removeDeviceCallService: "Call service layer to remove device configuration, device ID",
    removeDeviceResult: "Remove device configuration result",
    removeDeviceSuccess: "Remove device configuration successful, device ID",
    removeDeviceFailed: "Remove device configuration failed, device ID",
    removeDeviceException: "Remove device configuration exception, device ID",
    getDeviceListStart: "Start getting device configuration list",
    getDeviceListCallService: "Call service layer to get device configuration list",
    getDeviceListSuccess: "Successfully get device configuration list, device count",
    getDeviceListException: "Get device configuration list exception"
  },
  paramService: {
    getDiffParamComplete: "Comparison complete, difference groups",
    getAllDiffParamError: "getAllDiffParam error",
    getParamInfoEntry: "getParamInfo entry parameters",
    getParamInfoReturn: "getParamInfo return",
    getParamInfoError: "getParamInfo exception",
    startGetParamInfo: "Start getting parameter settings, pagination",
    getAllParamInfoStart: "Start getting all parameter settings",
    getAllParamInfoSuccess: "Successfully got all parameter settings, total",
    modifyParamStart: "Start modifying parameter settings",
    validateParam: "Validate parameter item",
    validateFailed: "Parameter validation failed",
    validatePassed: "Parameter validation passed, ready to send",
    setTimeout: "Set timeout",
    sendResponse: "Send response",
    modifySuccess: "Modification successful",
    sendFailed: "Send failed",
    businessError: "Business error",
    getAllDiffParamStart: "Start batch parameter comparison",
    excelParseFailed: "Excel parsing failed",
    csvParseFailed: "CSV parsing failed",
    xmlParseFailed: "XML parsing failed",
    fileParseComplete: "File parsing complete",
    getDiffParamStart: "Start single group parameter comparison",
    diffComplete: "Comparison complete, difference items",
    importParamStart: "Start importing parameter settings",
    paramReady: "Parameters ready to send",
    importSuccess: "Import successful",
    exportAllParamStart: "Start exporting all parameter settings",
    exportComplete: "Export complete",
    exportParamStart: "Start exporting group parameters",
    getGroupItemsStart: "Get group parameter items",
    getParamValueFailed: "Failed to get parameter value",
    getGroupItemsComplete: "Get complete, parameter items",
    getAllGroupItemsStart: "Get all group parameter items",
    groupParamCount: "Group: {group}, parameter items: {count}",
    getCurrentRunAreaStart: "Get current run area",
    getCurrentRunAreaSuccess: "Get successful",
    getCurrentRunAreaFailed: "Get failed",
    selectRunAreaStart: "Select setting area",
    runAreaEmpty: "Setting area cannot be empty",
    selectRunAreaSuccess: "Select successful",
    selectRunAreaFailed: "Select failed"
  },
  debugInfoMenuService: {
    initialized: "Initialization complete",
    getDebugInfoEntry: "getDebugInfo entry parameters",
    getTreeMenuError: "getTreeMenu exception",
    getTreeMenuComplete: "Processing complete, menu count"
  },
  deviceInfoController: {
    initialized: "Controller initialized",
    getDeviceInfoStart: "Start getting device information, request parameters",
    getDeviceInfoCheckConnection: "Check device connection status",
    getDeviceInfoNotConnected: "Device not connected, cannot get device information",
    getDeviceInfoConnected: "Device connected, call service layer to get device information",
    getDeviceInfoSuccess: "Successfully get device information, result count",
    getDeviceInfoException: "Get device information exception",
    exportDeviceInfoStart: "Start exporting device information, request parameters",
    exportDeviceInfoCheckConnection: "Check device connection status",
    exportDeviceInfoNotConnected: "Device not connected, cannot export device information",
    exportDeviceInfoValidateParams: "Validate export parameters, data count",
    exportDeviceInfoPath: "export path",
    exportDeviceInfoEmptyData: "Export data is empty",
    exportDeviceInfoEmptyPath: "Export file path is empty",
    exportDeviceInfoFileExtension: "File extension",
    exportDeviceInfoUnsupportedFormat: "Unsupported file format",
    exportDeviceInfoDirPath: "Export directory path",
    exportDeviceInfoCreateDir: "Create export directory",
    exportDeviceInfoCreateDirFailed: "Create export directory failed",
    exportDeviceInfoCallService: "Call service layer to export device information",
    exportDeviceInfoSuccess: "Device information export successful, export path",
    exportDeviceInfoException: "Export device information exception"
  },
  variableController: {
    getVariableEntry: "getVariable entry parameters",
    getVariableReturn: "Get variable method return log",
    getVariableException: "Get variable method exception log",
    addVariableEntry: "addVariable entry parameters",
    addVariableReturn: "Add variable method return log",
    addVariableException: "Add variable method exception log",
    modifyVariableEntry: "modifyVariable entry parameters",
    modifyVariableReturn: "Modify variable method return log",
    modifyVariableException: "Modify variable method exception log",
    deleteVariableEntry: "deleteVariable entry parameters",
    deleteVariableReturn: "Delete variable method return log",
    deleteVariableException: "Delete variable method exception log",
    exportVariableEntry: "exportVariable entry parameters",
    exportVariableEmptyPath: "Export path cannot be empty",
    exportVariableReturn: "Export variable method return log",
    exportVariableException: "Export variable method exception log",
    importVariableEntry: "importVariable entry parameters",
    importVariableEmptyPath: "Import path cannot be empty",
    importVariableReturn: "Import variable method return log",
    importVariableException: "Import variable method exception log"
  },
  paramController: {
    initialized: "Controller initialized",
    getParamStart: "Start getting device parameters, request parameters",
    getParamNotConnected: "Device not connected, cannot get device parameters",
    getParamConnected: "Device connected, call service layer to get device parameters",
    getParamSuccess: "Successfully get device parameters, result count",
    getParamException: "Get device parameters exception",
    getAllParamStart: "Start getting all device parameters, request parameters",
    getAllParamNotConnected: "Device not connected, cannot get all device parameters",
    getAllParamConnected: "Device connected, call service layer to get all device parameters",
    getAllParamSuccess: "Successfully get all device parameters, result count",
    getAllParamException: "Get all device parameters exception",
    confirmParamStart: "Start modifying device parameters, request parameters",
    confirmParamNotConnected: "Device not connected, cannot modify device parameters",
    confirmParamConnected: "Device connected, call service layer to modify device parameters",
    confirmParamSuccess: "Modify device parameters successful, result",
    confirmParamException: "Modify device parameters exception",
    getDiffParamStart: "Start getting parameter differences, request parameters",
    getDiffParamNotConnected: "Device not connected, cannot get parameter differences",
    getDiffParamPath: "Import path",
    getDiffParamEmptyPath: "Import path is empty",
    getDiffParamConnected: "Device connected, call service layer to get parameter differences",
    getDiffParamSuccess: "Successfully get parameter differences, result",
    getDiffParamException: "Get parameter differences exception",
    getAllDiffParamStart: "Start getting all parameter differences, request parameters",
    getAllDiffParamNotConnected: "Device not connected, cannot get all parameter differences",
    getAllDiffParamPath: "Import path",
    getAllDiffParamEmptyPath: "Import path is empty",
    getAllDiffParamConnected: "Device connected, call service layer to get all parameter differences",
    getAllDiffParamSuccess: "Successfully get all parameter differences, result",
    getAllDiffParamException: "Get all parameter differences exception",
    importParamStart: "Start importing device parameters, request parameters",
    importParamNotConnected: "Device not connected, cannot import device parameters",
    importParamConnected: "Device connected, call service layer to import device parameters",
    importParamSuccess: "Import device parameters successful, result",
    importParamException: "Import device parameters exception",
    exportParamStart: "Start exporting device parameters, request parameters",
    exportParamNotConnected: "Device not connected, cannot export device parameters",
    exportParamPath: "Export path",
    exportParamEmptyPath: "Export path is empty",
    exportParamConnected: "Device connected, call service layer to export device parameters",
    exportParamSuccess: "Export device parameters successful, result",
    exportParamException: "Export device parameters exception",
    exportAllParamStart: "Start exporting all device parameters, request parameters",
    exportAllParamNotConnected: "Device not connected, cannot export all device parameters",
    exportAllParamPath: "Export path",
    exportAllParamEmptyPath: "Export path is empty",
    exportAllParamConnected: "Device connected, call service layer to export all device parameters",
    exportAllParamSuccess: "Export all device parameters successful, result",
    exportAllParamException: "Export all device parameters exception",
    getCurrentRunAreaStart: "Start getting current run area, request parameters",
    getCurrentRunAreaNotConnected: "Device not connected, cannot get current run area",
    getCurrentRunAreaConnected: "Device connected, call service layer to get current run area",
    getCurrentRunAreaSuccess: "Successfully get current run area, result",
    getCurrentRunAreaException: "Get current run area exception",
    selectRunAreaStart: "Start selecting parameter area, request parameters",
    selectRunAreaNotConnected: "Device not connected, cannot select parameter area",
    selectRunAreaConnected: "Device connected, call service layer to select parameter area",
    selectRunAreaSuccess: "Successfully select parameter area, result",
    selectRunAreaException: "Select parameter area exception"
  },
  remoteControlController: {
    ykSelectEntry: "Remote control select method entry log",
    ykSelectReturn: "Remote control select method return log",
    ykSelectException: "Remote control select method exception log"
  },
  baseController: {
    getDeviceInfoStart: "Start getting device information, device ID",
    getDeviceInfoSuccess: "Successfully get device information, device ID",
    getDeviceInfoNotFound: "Device information not found, device ID",
    getDeviceInfoFailed: "Failed to get device information, device ID"
  },
  debugInfoMenuController: {
    initialized: "Controller initialized",
    getDeviceMenuTreeStart: "Start getting device menu tree, device ID",
    getDeviceMenuTreeCheckConnection: "Check device connection status, device ID",
    getDeviceMenuTreeNotConnected: "Device not connected, cannot get menu tree, device ID",
    getDeviceMenuTreeConnected: "Device connected, call service layer to get menu tree",
    getDeviceMenuTreeSuccess: "Successfully get device menu tree, menu item count",
    getDeviceMenuTreeEmpty: "Menu tree is empty, device ID",
    getDeviceMenuTreeException: "Get device menu tree exception, device ID",
    getTreeItemByNameStart: "Start getting menu item, request parameters",
    getTreeItemByNameCheckConnection: "Check device connection status, device ID",
    getTreeItemByNameNotConnected: "Device not connected, cannot get menu item, device ID",
    getTreeItemByNameConnected: "Device connected, call service layer to get menu item",
    getTreeItemByNameSuccess: "Successfully get menu item, result",
    getTreeItemByNameNotFound: "Menu item not found, device ID",
    getTreeItemByNameException: "Get menu item exception, device ID",
    getGroupInfoListStart: "Start getting group information, request parameters",
    getGroupInfoListCheckConnection: "Check device connection status, device ID",
    getGroupInfoListNotConnected: "Device not connected, cannot get group information, device ID",
    getGroupInfoListConnected: "Device connected, call service layer to get group information, device ID",
    getGroupInfoListSuccess: "Successfully get group information, result",
    getGroupInfoListNotFound: "Group information not found, device ID",
    getGroupInfoListException: "Get group information exception, device ID"
  },
  deviceFileController: {
    initialized: "Controller initialized",
    getDeviceFileStart: "Start getting device file directory, request parameters",
    getDeviceFileCheckConnection: "Check device connection status",
    getDeviceFileNotConnected: "Device not connected, cannot get file directory",
    getDeviceFileConnected: "Device connected, call service layer to get file directory",
    getDeviceFileSuccess: "Successfully get device file directory",
    getDeviceFileException: "Get device file directory exception",
    uploadDeviceFileStart: "Start uploading device file, request parameters",
    uploadDeviceFileCheckConnection: "Check device connection status",
    uploadDeviceFileNotConnected: "Device not connected, cannot upload file",
    uploadDeviceFileConnected: "Device connected, call service layer to upload file",
    uploadDeviceFileSuccess: "File upload successful, result",
    uploadDeviceFileException: "File upload exception",
    cancelUploadDeviceFileStart: "Start canceling file upload, request parameters",
    cancelUploadDeviceFileCheckConnection: "Check device connection status",
    cancelUploadDeviceFileNotConnected: "Device not connected, cannot cancel upload",
    cancelUploadDeviceFileConnected: "Device connected, call service layer to cancel upload",
    cancelUploadDeviceFileSuccess: "Cancel upload successful, result",
    cancelUploadDeviceFileException: "Cancel upload exception",
    downloadDeviceFileStart: "Start downloading device file, request parameters",
    downloadDeviceFileCheckConnection: "Check device connection status",
    downloadDeviceFileNotConnected: "Device not connected, cannot download file",
    downloadDeviceFileConnected: "Device connected, call service layer to download file",
    downloadDeviceFileSuccess: "File download successful, result",
    downloadDeviceFileException: "File download exception",
    cancelDownloadDeviceFileStart: "Start canceling file download, request parameters",
    cancelDownloadDeviceFileCheckConnection: "Check device connection status",
    cancelDownloadDeviceFileNotConnected: "Device not connected, cannot cancel download",
    cancelDownloadDeviceFileConnected: "Device connected, call service layer to cancel download",
    cancelDownloadDeviceFileSuccess: "Cancel download successful, result",
    cancelDownloadDeviceFileException: "Cancel download exception",
    importDownloadDeviceFileStart: "Start importing downloaded device file, request parameters",
    importDownloadDeviceFileNotConnected: "Device not connected, cannot import file",
    importDownloadDeviceFileConnected: "Device connected, call service layer to import file",
    importDownloadDeviceFileSuccess: "File import successful, result",
    importDownloadDeviceFileException: "File import exception",
    exportDownloadDeviceFileStart: "Start exporting downloaded device file, request parameters",
    exportDownloadDeviceFileNotConnected: "Device not connected, cannot export file",
    exportDownloadDeviceFileEmptyPath: "Export path is empty",
    exportDownloadDeviceFileCallService: "Call service layer to export file",
    exportDownloadDeviceFileSuccess: "File export successful, result",
    exportDownloadDeviceFileException: "File export exception"
  },
  deviceOperationController: {
    initialized: "Controller initialized",
    manualWaveStart: "Start manual wave recording, request parameters",
    manualWaveCheckConnection: "Check device connection status",
    manualWaveNotConnected: "Device not connected, cannot execute manual wave recording",
    manualWaveConnected: "Device connected, call service layer to execute manual wave recording",
    manualWaveSuccess: "Manual wave recording executed successfully, result",
    manualWaveException: "Manual wave recording execution exception",
    clearWaveStart: "Start clearing wave report, request parameters",
    clearWaveCheckConnection: "Check device connection status",
    clearWaveNotConnected: "Device not connected, cannot clear wave report",
    clearWaveConnected: "Device connected, call service layer to clear wave report",
    clearWaveSuccess: "Clear wave report successful, result",
    clearWaveException: "Clear wave report exception",
    resetDeviceStart: "Start device reset, request parameters",
    resetDeviceCheckConnection: "Check device connection status",
    resetDeviceNotConnected: "Device not connected, cannot execute device reset",
    resetDeviceConnected: "Device connected, call service layer to execute device reset",
    resetDeviceSuccess: "Device reset executed successfully, result",
    resetDeviceException: "Device reset execution exception",
    clearReportStart: "Start clearing report, request parameters",
    clearReportCheckConnection: "Check device connection status",
    clearReportNotConnected: "Device not connected, cannot clear report",
    clearReportConnected: "Device connected, call service layer to clear report",
    clearReportSuccess: "Clear report successful, result",
    clearReportException: "Clear report exception",
    rebootDeviceStart: "Start device reboot, request parameters",
    rebootDeviceCheckConnection: "Check device connection status",
    rebootDeviceNotConnected: "Device not connected, cannot execute device reboot",
    rebootDeviceConnected: "Device connected, call service layer to execute device reboot",
    rebootDeviceSuccess: "Device reboot executed successfully, result",
    rebootDeviceException: "Device reboot execution exception"
  },
  deviceSummaryController: {
    initialized: "Controller initialized",
    getSGCountStart: "Start getting SG count, request parameters",
    getSGCountCheckConnection: "Check device connection status, device ID",
    getSGCountNotConnected: "Device not connected, cannot get SG count, device ID",
    getSGCountConnected: "Device connected, call service layer to get SG count",
    getSGCountSuccess: "Successfully get SG count, result",
    getSGCountException: "Get SG count exception, device ID",
    getSummaryInfoStart: "Start getting device summary information, request parameters",
    getSummaryInfoCheckConnection: "Check device connection status",
    getSummaryInfoNotConnected: "Device not connected, cannot get device summary information",
    getSummaryInfoConnected: "Device connected, call service layer to get device summary information",
    getSummaryInfoSuccess: "Successfully get device summary information, result",
    getSummaryInfoException: "Get device summary information exception",
    getParamSummaryStart: "Start getting parameter summary information, request parameters",
    getParamSummaryCheckConnection: "Check device connection status",
    getParamSummaryNotConnected: "Device not connected, cannot get parameter summary information",
    getParamSummaryConnected: "Device connected, call service layer to get parameter summary information",
    getParamSummarySuccess: "Successfully get parameter summary information, result",
    getParamSummaryException: "Get parameter summary information exception"
  },
  deviceTimeController: {
    initialized: "Controller initialized",
    getDeviceTimeStart: "Start getting device time, request parameters",
    getDeviceTimeCheckConnection: "Check device connection status",
    getDeviceTimeNotConnected: "Device not connected, cannot get device time",
    getDeviceTimeConnected: "Device connected, call service layer to get device time",
    getDeviceTimeSuccess: "Successfully get device time, result",
    getDeviceTimeException: "Get device time exception",
    writeDeviceTimeStart: "Start setting device time, request parameters",
    writeDeviceTimeCheckConnection: "Check device connection status",
    writeDeviceTimeNotConnected: "Device not connected, cannot set device time",
    writeDeviceTimeConnected: "Device connected, call service layer to set device time",
    writeDeviceTimeSuccess: "Successfully set device time, result",
    writeDeviceTimeException: "Set device time exception"
  },
  realEventController: {
    initialized: "Controller initialized",
    subRealEventStart: "Start subscribing to real-time events, request parameters",
    subRealEventCallService: "Call service layer to subscribe to real-time events",
    subRealEventException: "Subscribe to real-time events exception",
    subRealEventServiceResult: "Subscribe to real-time events service layer return result",
    subRealEventFailed: "Subscribe to real-time events failed, error information",
    subRealEventServiceError: "Subscribe to events service error, error code",
    subRealEventSuccess: "Subscribe to real-time events successful",
    unSubRealEventStart: "Start unsubscribing from real-time events, request parameters",
    unSubRealEventCallService: "Call service layer to unsubscribe from real-time events",
    unSubRealEventException: "Unsubscribe from real-time events exception",
    unSubRealEventServiceResult: "Unsubscribe from real-time events service layer return result",
    unSubRealEventFailed: "Unsubscribe from real-time events failed, error information",
    unSubRealEventServiceError: "Unsubscribe from events service error, error code",
    unSubRealEventSuccess: "Unsubscribe from real-time events successful"
  },
  remoteYxAndYcController: {
    ykycGetGroupDataEntry: "ykycGetGroupData entry parameters",
    ykycGetGroupDataReturn: "ykycGetGroupData return",
    ykycGetGroupDataException: "ykycGetGroupData exception",
    exportAllDataEntry: "exportAllData entry parameters",
    exportAllDataEmptyPath: "Export path cannot be empty",
    exportAllDataReturn: "exportAllData return",
    exportAllDataException: "exportAllData exception"
  },
  deviceConnectController: {
    initialized: "Controller initialized",
    connectDeviceStart: "Start connecting device, connection parameters",
    connectDeviceCallService: "Call service layer to connect device",
    connectDeviceServiceResult: "Service layer return result",
    connectDeviceSuccess: "Device connection successful",
    connectDeviceFailed: "Connect device failed, error information",
    connectDeviceException: "Connect device exception",
    disconnectDeviceStart: "Start disconnecting device, device ID",
    disconnectDeviceCheckStatus: "Check device connection status",
    disconnectDeviceAlready: "Device already disconnected, device ID",
    disconnectDeviceCallService: "Call service layer to disconnect device",
    disconnectDeviceResult: "Disconnect result",
    disconnectDeviceSuccess: "Device disconnection successful, device ID",
    disconnectDeviceException: "Disconnect device exception, device ID",
    disconnectDeviceFailed: "Disconnect device failed, device ID"
  },
  paramController: {
    initialized: "Controller initialized",
    getParamStart: "Start getting device parameters, request parameters",
    getParamNotConnected: "Device not connected, cannot get device parameters",
    getParamConnected: "Device connected, call service layer to get device parameters",
    getParamSuccess: "Successfully get device parameters, result count",
    getParamException: "Get device parameters exception",
    getDiffParamEmptyPath: "Import path is empty",
    getAllDiffParamEmptyPath: "Import path is empty",
    exportParamEmptyPath: "Export path is empty",
    exportAllParamEmptyPath: "Export path is empty"
  },
  variableController: {
    getVariableEntry: "getVariable entry parameters",
    getVariableReturn: "getVariable return",
    getVariableException: "getVariable exception",
    exportVariableEmptyPath: "Export path cannot be empty",
    importVariableEmptyPath: "Import path cannot be empty"
  },
  remoteControlController: {
    initialized: "Controller initialized",
    ykSelectWithValueEntry: "ykSelectWithValue entry parameters",
    ykSelectWithValueReturn: "ykSelectWithValue return",
    ykSelectWithValueException: "ykSelectWithValue exception",
    ykExecuteException: "ykExecute exception",
    ykSelectValueCancelException: "ykSelectValueCancel exception"
  },

  // New service logs
  customInfoService: {
    getAllGroupsEntry: "getAllGroups entry parameters",
    addMenuEntry: "addMenu entry parameters",
    groupName: "group name",
    editMenuEntry: "editMenu entry parameters",
    newGroupKeyword: "new group keyword",
    deleteMenuEntry: "deleteMenu entry parameters",
    addReportEntry: "addReport entry parameters",
    reportName: "report name",
    newName: "new name",
    editReportEntry: "editReport entry parameters",
    newReportName: "new report name",
    deleteReportEntry: "deleteReport entry parameters",
    getLGReportsEntry: "getLGReports entry parameters",
    getLGReportsSuccess: "getLGReports success",
    lgReportsCount: "LG reports count",
    getLGReportsError: "getLGReports exception"
  },

  deviceOperationService: {
    rebootDeviceEntry: "rebootDevice entry parameters",
    rebootDeviceStart: "device reboot started",
    rebootDeviceReturn: "rebootDevice return",
    rebootDeviceError: "rebootDevice exception",
    clearReportEntry: "clearReport entry parameters",
    clearReportStart: "clear report started",
    clearReportReturn: "clearReport return",
    clearReportError: "clearReport exception",
    resetDeviceEntry: "resetDevice entry parameters",
    resetDeviceStart: "device reset started",
    resetDeviceReturn: "resetDevice return",
    resetDeviceError: "resetDevice exception",
    clearWaveEntry: "clearWave entry parameters",
    clearWaveStart: "clear wave started",
    clearWaveReturn: "clearWave return",
    clearWaveError: "clearWave exception",
    manualWaveEntry: "manualWave entry parameters",
    manualWaveStart: "manual wave started",
    manualWaveReturn: "manualWave return",
    manualWaveError: "manualWave exception"
  },

  deviceSummaryService: {
    getSGCountEntry: "getSGCount entry parameters",
    getSGCountReturn: "getSGCount return",
    getSGCountError: "getSGCount exception",
    getSummaryInfoEntry: "getSummaryInfo entry parameters",
    getSummaryInfoReturn: "getSummaryInfo return",
    getSummaryInfoError: "getSummaryInfo exception",
    getParamSummaryEntry: "getParamSummary entry parameters",
    getParamSummaryReturn: "getParamSummary return",
    getParamSummaryError: "getParamSummary exception"
  },

  deviceTimeService: {
    getDeviceTimeEntry: "getDeviceTime entry parameters",
    getDeviceTimeReturn: "getDeviceTime return",
    getDeviceTimeError: "getDeviceTime exception",
    writeDeviceTimeEntry: "writeDeviceTime entry parameters",
    writeDeviceTimeReturn: "writeDeviceTime return",
    writeDeviceTimeError: "writeDeviceTime exception"
  },

  remoteYxAndYcService: {
    ykycGetGroupDataEntry: "ykycGetGroupData entry parameters",
    ykycGetGroupDataReturn: "ykycGetGroupData return",
    ykycGetGroupDataError: "ykycGetGroupData exception",
    ykycGetAllGroupDataEntry: "ykycGetAllGroupData entry parameters",
    exportAllValError: "exportAllVal exception"
  },

  paramService: {
    getDiffParamStart: "start single group parameter comparison",
    excelParseFailed: "Excel parsing failed",
    csvParseFailed: "CSV parsing failed",
    xmlParseFailed: "XML parsing failed",
    fileParseComplete: "file parsing completed",
    importParamStart: "start importing parameter settings",
    paramPrepareToSend: "parameters ready to send",
    importSuccess: "import successful",
    sendFailed: "send failed",
    businessError: "business error",
    importParamError: "importParam exception",
    exportAllParamStart: "start exporting all parameter settings",
    exportAllParamComplete: "export completed",
    exportAllParamError: "exportAllParam exception",
    exportParamStart: "start exporting group parameters",
    exportParamComplete: "export completed",
    exportParamError: "exportParam exception",
    getGroupItemsStart: "get group parameter items",
    getParamValuesFailed: "failed to get parameter values",
    getGroupItemsComplete: "get completed, parameter item count",
    getAllGroupItemsStart: "get all group parameter items",
    getAllGroupItemsGetValuesFailed: "failed to get parameter values",
    getAllGroupItemsGroupInfo: "group",
    paramItemCount: "parameter item count",
    getCurrentRunAreaStart: "get current run area",
    getCurrentRunAreaSuccess: "get successful",
    getCurrentRunAreaFailed: "get failed",
    getCurrentRunAreaError: "getCurrentRunArea exception",
    selectRunAreaStart: "select setting area",
    selectRunAreaSuccess: "select successful",
    selectRunAreaFailed: "select failed",
    selectRunAreaError: "selectRunArea exception",
    runAreaEmpty: "run area cannot be empty",
    getAllParamInfoError: "getAllParamInfo exception",
    modifyParamError: "modifyParam exception",
    getDiffParamError: "getDiffParam exception"
  }
};
