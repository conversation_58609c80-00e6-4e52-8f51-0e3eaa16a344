import { IECReq } from "../../interface/debug/request";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { MENU_ITEM } from "../../data/debug/menuItem";
import { ParamSummaryInfo, SummaryInfo } from "../../interface/debug/devicesummary";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import { DebugInfoMenu } from "../../interface/debug/debuginfo";
import { debugInfoMenuService } from "./debuginfomenu";

/**
 * 装置汇总信息Service
 * 负责装置各类信息的统计、汇总等业务逻辑
 * <AUTHOR>
 * @class
 */
class DeviceSummaryService {
  // 获取SG数量
  async getSGCount(req: IECReq<any>): Promise<number> {
    logger.info(
      `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSGCountEntry")}:`,
      JSON.stringify(req)
    );
    try {
      const sgCount = debugInfoMenuService.getTreeItemByFc(
        [MENU_ITEM.SG],
        req.head.id,
        false
      ).length;
      logger.info(
        `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSGCountReturn")}:`,
        sgCount
      );
      return sgCount;
    } catch (error) {
      logger.error(
        `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSGCountError")}:`,
        error
      );
      throw error;
    }
  }

  // 获取汇总信息
  async getSummaryInfo(req: IECReq<any>) {
    logger.info(
      `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSummaryInfoEntry")}:`,
      JSON.stringify(req)
    );
    const device = GlobalDeviceData.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    try {
      if (device && device.debugInfo) {
        const summaryinfo: SummaryInfo = {
          settingNum: 0,
          ykNum: 0,
          driveNum: 0,
          ycNum: 0,
          yxNum: 0,
        };
        const sgCount = debugInfoMenuService.getTreeItemByFc(
          [MENU_ITEM.SG],
          req.head.id,
          false
        ).length;
        const spCount = debugInfoMenuService.getTreeItemByFc(
          [MENU_ITEM.SP],
          req.head.id,
          false
        ).length;
        // logger.info("spCount============", spCount);
        summaryinfo.settingNum += sgCount + spCount || 0;

        const stCount = debugInfoMenuService.getTreeItemByFc(
          [MENU_ITEM.ST],
          req.head.id,
          false
        ).length;
        const mxCount = debugInfoMenuService.getTreeItemByFc(
          [MENU_ITEM.MX],
          req.head.id,
          false
        ).length;
        summaryinfo.yxNum += stCount || 0;
        summaryinfo.ycNum += mxCount || 0;

        const ykNum = debugInfoMenuService.getTreeItemByFc(
          [MENU_ITEM.CO],
          req.head.id,
          false
        ).length;
        const driveNum = debugInfoMenuService.getTreeItemByFc(
          [MENU_ITEM.BO],
          req.head.id,
          false
        ).length;
        summaryinfo.ykNum += ykNum || 0;
        summaryinfo.driveNum += driveNum || 0;

        logger.info(
          `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSummaryInfoReturn")}:`,
          summaryinfo
        );
        return summaryinfo;
      }
      throw new Error(t("errors.noDataRetrieved"));
    } catch (error) {
      logger.error(
        `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSummaryInfoError")}:`,
        error
      );
      throw error;
    }
  }

  // 获取定值汇总信息
  async getParamSummary(req: IECReq<any>) {
    logger.info(
      `[DeviceSummaryService] ${t("logs.deviceSummaryService.getParamSummaryEntry")}:`,
      JSON.stringify(req)
    );
    const device = GlobalDeviceData.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    try {
      if (device && device.debugInfo) {
        const summaryinfo: ParamSummaryInfo[] = [];

        const devicemenus = device.debugInfo.menus;
        parseSettingMenu(devicemenus, summaryinfo);
        logger.info(
          `[DeviceSummaryService] ${t("logs.deviceSummaryService.getParamSummaryReturn")}:`,
          "success"
        );
        return summaryinfo;
      }
      throw new Error(t("errors.noDataRetrieved"));
    } catch (error) {
      logger.error(
        `[DeviceSummaryService] ${t("logs.deviceSummaryService.getParamSummaryError")}:`,
        error
      );
      throw error;
    }

    function parseSettingMenu(
      devicemenus: DebugInfoMenu[],
      summaryinfo: ParamSummaryInfo[]
    ) {
      devicemenus.forEach((item) => {
        if (item.fc == MENU_ITEM.SG || item.fc == MENU_ITEM.SP) {
          summaryinfo.push({
            name: item.desc,
            value: item.items?.length || 0,
          });
        }
        if (item.menus) {
          parseSettingMenu(item.menus, summaryinfo);
        }
      });
    }
  }
}

DeviceSummaryService.toString = () => "[class DeviceSummaryService]";
const deviceSummaryService = new DeviceSummaryService();

export { DeviceSummaryService, deviceSummaryService };
