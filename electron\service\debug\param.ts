"use strict";

import { logger } from "ee-core/log";
import { ApiPagination } from "../../data/debug/apiPagenation";
import ParamItem from "../../data/debug/paramItem"; // 确保正确导入
import { ExcelExporter } from "../../utils/excelUtils"; // 引入ExcelExporter
import { IECReq } from "../../interface/debug/request";
import ClientDeviceGlobal from "../../data/debug/globalDeviceData";
import { XmlFileManager } from "../../utils/xmlFileManager";
import { SgValueReadRequestData } from "iec-upadrpc/dist/src/data";
import { DebugInfoItem, DebugInfoMenu } from "../../interface/debug/debuginfo";
import { t } from "../../data/i18n/i18n";

import {
  isValid,
  isEquals,
  removeLastCarriage,
  formatByStep,
} from "../../utils/common";
import { ApiResponse } from "../../data/debug/apiResponse";
import {
  ERROR_CODES,
  getErrorMessage,
  handleCustomResponse,
} from "../../data/debug/errorCodes";

import { Column } from "../../interface/debug/exportTypes";
import { CsvHandler } from "../../utils/csvHandler";
import { XmlHandler } from "../../utils/xmlHandler";
import { IpUtils } from "../../utils/ipUtils";
import { AllDiffItem, DiffItem } from "../../data/debug/diffItem";
import { ImportResult, SheetData } from "../../interface/debug/sheetDara";
import { deviceOperateService } from "../../service/debug/deviceoperate";
import { MENU_ITEM } from "../../data/debug/menuItem";
import IECCONSTANTS from "../../data/debug/iecConstants";
import { debugInfoMenuService } from "./debuginfomenu";

/**
 * 参数定值Service
 * 负责装置参数定值的查询、修改、导入导出、差异比对等业务逻辑
 * <AUTHOR>
 * @class
 */
class ParamService {
  // 导出工具类
  private excelExporter: ExcelExporter;
  private xmlFileManager: XmlFileManager;

  /**
   * 构造函数，初始化导出工具和XML文件管理器
   */
  constructor() {
    this.excelExporter = new ExcelExporter();
    this.xmlFileManager = new XmlFileManager();
  }

  /**
   * 分页获取装置参数定值信息
   * @param req 请求体，包含分页信息、定值区等
   * @returns ApiPagination<ParamItem> 分页参数定值列表
   */
  async getParamInfo(req: IECReq<any>): Promise<ApiPagination<ParamItem>> {
    // 分页获取装置参数定值信息方法入口日志
    logger.info(
      `[ParamService] ${t("logs.paramService.getParamInfoEntry")}:`,
      JSON.stringify(req)
    );
    try {
      logger.info(
        `[ParamService.getParamInfo] ${t("logs.paramService.startGetParamInfo")}：`,
        req.data.pageNum,
        req.data.pageSize
      );
      const { pageNum, pageSize } = req.data;
      // 获取分组参数项
      const resultList = await this.getGroupItems(req, false);
      const apiPagination = new ApiPagination<ParamItem>(
        resultList.total,
        pageSize,
        pageNum,
        resultList.data
      );
      logger.info(
        `[ParamService] ${t("logs.paramService.getParamInfoReturn")}:`,
        apiPagination.total
      );
      return apiPagination;
    } catch (error) {
      logger.error(
        `[ParamService] ${t("logs.paramService.getParamInfoError")}:`,
        error
      );
      throw error;
    }
  }

  /**
   * 获取所有装置参数定值信息（不分页）
   * @param req 请求体
   * @returns ApiPagination<ParamItem>
   */
  async getAllParamInfo(req: IECReq<any>): Promise<ApiPagination<ParamItem>> {
    try {
      logger.info(
        `[ParamService] ${t("logs.paramService.getAllParamInfoStart")}`
      );
      const { pageNum, pageSize } = req.data;
      const resultList = await this.getGroupItems(req, false);
      const apiPagination = new ApiPagination<ParamItem>(
        resultList.total,
        pageSize,
        pageNum,
        resultList.data
      );
      logger.info(
        `[ParamService] ${t("logs.paramService.getAllParamInfoSuccess")}：`,
        resultList.total
      );
      return apiPagination;
    } catch (error) {
      logger.error(`[ParamService] ${t("logs.paramService.getAllParamInfoError")}`, error);
      throw error;
    }
  }

  /**
   * 修改装置定值信息
   * @param req 请求体，包含定值区和参数项
   * @returns boolean 修改是否成功
   */
  async modifyParam(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info(`[ParamService] ${t("logs.paramService.modifyParamStart")}`);
      const devInfo = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = devInfo?.deviceClient;
      const objMap = devInfo?.debugItemObjMap;
      let checkMsg = "";
      const reqestData = req.data;
      reqestData.forEach((element) => {
        logger.info(
          `[ParamService] ${t("logs.paramService.validateParam")}`,
          element.name,
          element.v
        );
        const item = objMap?.get(element.name);
        if (
          !isValid(
            String(element.v),
            item?.type || "",
            this.getRealValue(item?.type || "", item?.s_min, item?.step),
            this.getRealValue(item?.type || "", item?.s_max, item?.step),
            Number(item?.step)
          )
        ) {
          checkMsg +=
            element.name +
            t("errors.description") +
            item?.desc +
            t("errors.errorReason") +
            this.getRealValue(String(item?.type), element.v, item?.step) +
            t("errors.invalidValue") +
            "\n";
        }
        element.v = String(
          this.parseAddrToNum(item?.type || "", String(element.v))
        );
      });
      if (checkMsg.length > 0) {
        logger.error(
          `[ParamService] ${t("logs.paramService.validateFailed")}`,
          checkMsg
        );
        throw new Error(removeLastCarriage(checkMsg));
      }
      logger.info(
        `[ParamService] ${t("logs.paramService.validatePassed")}`,
        reqestData
      );
      // 设置定值修改超时时间
      const selectDeivce = await deviceOperateService.getById(req.head.id);
      client?.setRequestTimeout(
        selectDeivce?.paramTimeout || IECCONSTANTS.WRITE_SETTING_TIMEOUT
      );
      logger.info(
        `[ParamService] ${t("logs.paramService.setTimeout")}`,
        selectDeivce?.paramTimeout
      );
      const response = await client?.setEditSgValue(
        reqestData,
        true,
        IECCONSTANTS.BATCH_COUNT
      );
      logger.info(
        `[ParamService] ${t("logs.paramService.sendResponse")}`,
        response
      );
      if (response?.isSuccess()) {
        let msg = "";
        if (response.data.error == "none") {
          logger.info(`[ParamService] ${t("logs.paramService.modifySuccess")}`);
          return true;
        } else if (response.data.error == "itemError") {
          msg += t("errors.paramModifyError");
          const errdata = response.data.data;
          if (errdata && Array.isArray(errdata)) {
            errdata.forEach((element, index) => {
              msg +=
                req.data[index].name +
                t("errors.errorReason") +
                devInfo?.getServiceErrMsgByCode(String(element)) +
                "；\n";
            });
          }
        } else if (response.data.error == "confirmError") {
          msg += t("errors.paramConfirmError");
          msg += devInfo?.getServiceErrMsgByCode(String(response.data.data));
        }
        if (msg.length > 0) {
          logger.error(
            `[ParamService] ${t("logs.paramService.sendFailed")}`,
            msg
          );
          throw new Error(msg);
        }
      }
      // 获取业务错误信息
      logger.error(
        `[ParamService] ${t("logs.paramService.businessError")}`,
        response?.msg
      );
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(`[ParamService] ${t("logs.paramService.modifyParamError")}`, error);
      throw error;
    }
  }

  /**
   * 获取所有分组参数的差异（批量比对导入文件与设备参数）
   * @param req 请求体，包含文件路径、定值区等
   * @returns ApiResponse 差异比对结果
   */
  async getAllDiffParam(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info(
        `[ParamService] ${t("logs.paramService.getAllDiffParamStart")}`
      );
      const { path } = req.data;
      const allDiff: AllDiffItem[] = [];
      let parsedData: ImportResult = [];
      // 解析导入文件
      if (String(path).endsWith("xlsx")) {
        const keyMapping = {
          [t("params.headers.name")]: "paramName",
          [t("params.headers.value")]: "value",
        };
        try {
          parsedData = await this.excelExporter.parseMultiSheets(path, {
            keyMapping,
          });
        } catch (e: any) {
          logger.error(
            `[ParamService] ${t("logs.paramService.excelParseFailed")}`,
            e
          );
          return handleCustomResponse(
            ERROR_CODES.INTERNAL_ERROR,
            t("errors.excelFileParseError") + (e.message || e),
            []
          );
        }
      } else if (String(path).endsWith("csv")) {
        const columns: Column[] = [
          { header: t("params.headers.name"), key: "paramName", width: 20 },
          { header: t("params.headers.value"), key: "value", width: 15 },
        ];
        try {
          parsedData = await CsvHandler.importMuti(path, columns);
        } catch (e: any) {
          logger.error(
            `[ParamService] ${t("logs.paramService.csvParseFailed")}`,
            e
          );
          return handleCustomResponse(
            ERROR_CODES.INTERNAL_ERROR,
            t("errors.csvFileParseError") + (e.message || e),
            []
          );
        }
      } else if (String(path).endsWith("xml")) {
        const columns: Column[] = [
          { header: t("params.headers.name"), key: "paramName", width: 20 },
          { header: t("params.headers.value"), key: "value", width: 15 },
        ];
        try {
          parsedData = await XmlHandler.importMultiGroups(path, columns);
        } catch (e: any) {
          logger.error(
            `[ParamService] ${t("logs.paramService.xmlParseFailed")}`,
            e
          );
          return handleCustomResponse(
            ERROR_CODES.INTERNAL_ERROR,
            t("errors.xmlFileParseError") + (e.message || e),
            []
          );
        }
      }
      logger.info(`[ParamService] ${t("logs.paramService.fileParseComplete")}`);
      const excelData111 = this.findByName(
        parsedData,
        t("common.exportMatrixSettings")
      );
      logger.info("excelData:", excelData111?.data);

      // logger.info("parsedData:", parsedData[0]);
      let checkMsg = "";
      const importResult = await this.getAllGroupItems(req);
      importResult.forEach((result) => {
        const desc = result.sheetName;
        logger.info(desc);
        const data: ParamItem[] = result.data;
        const varimap = new Map<string, ParamItem>();
        data.forEach((item) => {
          varimap.set(item.paramName, item);
        });
        // 模拟数据
        const diffData: DiffItem[] = [];
        const excelData = this.findByName(parsedData, desc);
        let newData = excelData?.data;

        if (newData) {
          logger.info("newData", newData?.length);
          for (let i = 0; i < newData.length; i++) {
            const item = newData[i];
            const name = item.paramName;
            const value = item.value;
            const vari = varimap.get(name);

            if (vari && !isEquals(vari.value, value)) {
              if (
                !isValid(
                  String(value),
                  vari?.type || "",
                  String(vari?.minValue),
                  String(vari?.maxValue),
                  Number(vari?.step)
                )
              ) {
                checkMsg +=
                  t("errors.errorItem") +
                  name +
                  t("errors.errorReason") +
                  value +
                  t("errors.invalidValue") +
                  "\n";
              } else {
                diffData.push({
                  name: name,
                  description: vari.paramDesc,
                  minValue: vari.minValue,
                  maxValue: vari.maxValue,
                  step: vari.step,
                  unit: vari.unit,
                  inf: vari.inf,
                  oldValue: vari.value,
                  newValue: String(value),
                  grp: vari.grp,
                  type: vari.type,
                  grpName: desc || "",
                });
              }
            }
          }
          logger.info("checkMsg:", checkMsg);
          logger.info("diffData:", diffData);
          logger.info("[param] getDiffParam result", diffData.length);

          if (diffData.length > 0) {
            allDiff.push({
              grpname: desc || "",
              data: diffData,
            });
            // logger.info("[param] getDiffParam allDiff", allDiff);
          }
        }
      });

      if (checkMsg.length == 0) {
        checkMsg = t("errors.getFileDataSuccess");
      }
      logger.info(t("logs.paramService.getDiffParamComplete"), allDiff.length);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        removeLastCarriage(checkMsg),
        allDiff
      );
    } catch (error) {
      logger.error(t("logs.paramService.getAllDiffParamError"), error);
      throw error;
    }
  }

  private findByName(arr, targetName) {
    for (const item of arr) {
      if (item.grpname == targetName) {
        // logger.info("item:", item);
        return item;
      }
    }

    return null;
  }

  /**
   * 获取单个分组参数的差异（比对导入文件与设备参数）
   * @param req 请求体，包含文件路径、分组名、定值区等
   * @returns ApiResponse 差异比对结果
   */
  async getDiffParam(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info(`[ParamService] ${t("logs.paramService.getDiffParamStart")}`);
      const { path, grpName } = req.data;
      let parsedData: any[] = [];
      // 解析导入文件
      if (String(path).endsWith("xlsx")) {
        const keyMapping = {
          [t("params.headers.name")]: "paramName",
          [t("params.headers.value")]: "value",
        };
        try {
          parsedData = await this.excelExporter.parseExcel(
            path,
            keyMapping,
            grpName
          );
        } catch (e: any) {
          logger.error(
            `[ParamService] ${t("logs.paramService.excelParseFailed")}`,
            e
          );
          return handleCustomResponse(
            ERROR_CODES.INTERNAL_ERROR,
            t("errors.excelFileParseError") + (e.message || e),
            []
          );
        }
      } else if (String(path).endsWith("csv")) {
        const columns: Column[] = [
          { header: t("params.headers.name"), key: "paramName", width: 20 },
          { header: t("params.headers.value"), key: "value", width: 15 },
        ];
        try {
          parsedData = await CsvHandler.import(path, columns, grpName);
        } catch (e: any) {
          logger.error(
            `[ParamService] ${t("logs.paramService.csvParseFailed")}`,
            e
          );
          return handleCustomResponse(
            ERROR_CODES.INTERNAL_ERROR,
            t("errors.csvFileParseError") + (e.message || e),
            []
          );
        }
      } else if (String(path).endsWith("xml")) {
        const columns: Column[] = [
          { header: t("params.headers.name"), key: "paramName", width: 20 },
          { header: t("params.headers.value"), key: "value", width: 15 },
        ];
        try {
          parsedData = await XmlHandler.import(path, columns, grpName);
        } catch (e: any) {
          logger.error(
            `[ParamService] ${t("logs.paramService.xmlParseFailed")}`,
            e
          );
          return handleCustomResponse(
            ERROR_CODES.INTERNAL_ERROR,
            t("errors.xmlFileParseError") + (e.message || e),
            []
          );
        }
      }
      logger.info(`[ParamService] ${t("logs.paramService.fileParseComplete")}`);
      const result = await this.getGroupItems(req, true);
      const data: ParamItem[] = result.data;
      const varimap = new Map<string, ParamItem>();
      data.forEach((item) => {
        varimap.set(item.paramName, item);
      });
      // 模拟数据
      const diffData: DiffItem[] = [];
      let checkMsg = "";
      logger.info("parsedData:", parsedData.length);
      for (let i = 0; i < parsedData.length; i++) {
        const item = parsedData[i];
        const name = item.paramName;
        const value = item.value;
        const vari = varimap.get(name);
        if (vari && !isEquals(vari.value, value)) {
          if (
            !isValid(
              String(value),
              vari?.type || "",
              String(vari?.minValue),
              String(vari?.maxValue),
              Number(vari?.step)
            )
          ) {
            checkMsg +=
              t("errors.errorItem") +
              name +
              t("errors.errorReason") +
              value +
              t("errors.invalidValue") +
              "\n";
          } else {
            diffData.push({
              name: name,
              description: vari.paramDesc,
              minValue: vari.minValue,
              maxValue: vari.maxValue,
              step: vari.step,
              unit: vari.unit,
              inf: vari.inf,
              oldValue: vari.value,
              newValue: String(value),
              grp: vari.grp,
              type: vari.type,
              grpName: grpName,
            });
          }
        }
        if (checkMsg.length == 0) {
          checkMsg = t("errors.getFileDataSuccess");
        }
      }
      logger.info("checkMsg:", checkMsg);
      logger.info("[param] getDiffParam result", diffData.length);
      logger.info(
        "[ParamService] getDiffParam - 比对完成，差异项数：",
        diffData.length
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        removeLastCarriage(checkMsg),
        diffData
      );
    } catch (error) {
      logger.error(`[ParamService] ${t("logs.paramService.getDiffParamError")}`, error);
      throw error;
    }
  }

  /**
   * 导入参数定值
   * @param req 请求体，包含定值区和参数项
   * @returns boolean 导入是否成功
   */
  async importParam(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info(`[ParamService] ${t("logs.paramService.importParamStart")}`);
      const devInfo = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = devInfo?.deviceClient;
      logger.info(
        `[ParamService] ${t("logs.paramService.paramPrepareToSend")}`,
        req.data
      );
      // 设置定值修改超时时间
      const selectDeivce = await deviceOperateService.getById(req.head.id);
      client?.setRequestTimeout(selectDeivce?.paramTimeout || 30000);
      logger.info(
        `[ParamService] ${t("logs.paramService.setTimeout")}`,
        selectDeivce?.paramTimeout
      );
      const response = await client?.setEditSgValue(
        req.data,
        true,
        IECCONSTANTS.BATCH_COUNT
      );
      logger.info(
        `[ParamService] ${t("logs.paramService.sendResponse")}`,
        response
      );
      if (response?.isSuccess()) {
        let msg = "";
        if (response.data.error == "none") {
          logger.info(`[ParamService] ${t("logs.paramService.importSuccess")}`);
          return true;
        } else if (response.data.error == "itemError") {
          msg += t("errors.paramModifyError");
          const errdata = response.data.data;
          if (errdata && Array.isArray(errdata)) {
            errdata.forEach((element, index) => {
              msg +=
                req.data[index].name +
                t("errors.errorReason") +
                devInfo?.getServiceErrMsgByCode(String(element)) +
                "；\n";
            });
          }
        } else if (response.data.error == "confirmError") {
          msg += t("errors.paramConfirmError");
          msg +=
            devInfo?.getServiceErrMsgByCode(String(response.data.data)) + "\n";
        }
        if (msg.length > 0) {
          logger.error(
            `[ParamService] ${t("logs.paramService.sendFailed")}`,
            msg
          );
          throw new Error(removeLastCarriage(msg));
        }
      }
      logger.error(`[ParamService] ${t("logs.paramService.businessError")}`, response?.msg);
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(`[ParamService] ${t("logs.paramService.importParamError")}`, error);
      return false;
    }
  }

  /**
   * 导出所有参数定值（多分组）
   * @param req 请求体，包含导出路径、定值区等
   * @returns boolean 导出是否成功
   */
  async exportAllParam(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info(`[ParamService] ${t("logs.paramService.exportAllParamStart")}`);
      const result: SheetData[] = await this.getAllGroupItems(req);
      const columns: Column[] = [
        { header: t("params.headers.index"), key: "index", width: 10 },
        { header: t("params.headers.name"), key: "paramName", width: 20 },
        {
          header: t("params.headers.description"),
          key: "paramDesc",
          width: 30,
        },
        { header: t("params.headers.value"), key: "value", width: 15 },
        { header: t("params.headers.minValue"), key: "minValue", width: 15 },
        { header: t("params.headers.maxValue"), key: "maxValue", width: 15 },
        { header: t("params.headers.step"), key: "step", width: 10 },
        { header: t("params.headers.unit"), key: "unit", width: 10 },
      ];
      const { path } = req.data;
      // 根据文件类型导出
      if (String(path).endsWith("xlsx")) {
        await this.excelExporter.exportToMutilSheetExcel(result, columns, path);
      } else if (String(path).endsWith("csv")) {
        await CsvHandler.exportMuti(result, columns, path);
      } else if (String(path).endsWith("xml")) {
        await XmlHandler.exportMutiSheet(result, columns, path);
      }
      logger.info(`[ParamService] ${t("logs.paramService.exportAllParamComplete")}`);
      return true;
    } catch (error) {
      logger.error(`[ParamService] ${t("logs.paramService.exportAllParamError")}`, error);
      throw error;
    }
  }

  /**
   * 导出单个分组参数定值
   * @param req 请求体，包含分组名、导出路径、定值区等
   * @returns boolean 导出是否成功
   */
  async exportParam(req: IECReq<any>): Promise<boolean> {
    try {
      const { grpName } = req.data;
      logger.info(`[ParamService] ${t("logs.paramService.exportParamStart")}`, grpName);
      const result = await this.getGroupItems(req, true);
      const data: ParamItem[] = result.data;
      const columns: Column[] = [
        { header: t("params.headers.index"), key: "index", width: 10 },
        { header: t("params.headers.name"), key: "paramName", width: 20 },
        {
          header: t("params.headers.description"),
          key: "paramDesc",
          width: 30,
        },
        { header: t("params.headers.value"), key: "value", width: 15 },
        { header: t("params.headers.minValue"), key: "minValue", width: 15 },
        { header: t("params.headers.maxValue"), key: "maxValue", width: 15 },
        { header: t("params.headers.step"), key: "step", width: 10 },
        { header: t("params.headers.unit"), key: "unit", width: 10 },
      ];
      const { path } = req.data;
      const devInfo = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const menu = devInfo?.debugInfo?.menus;
      const menuItem: DebugInfoMenu = this.xmlFileManager.findElementByName(
        menu || [],
        grpName
      );
      // 根据文件类型导出
      if (String(path).endsWith("xlsx")) {
        await this.excelExporter.exportToExcel(
          data,
          columns,
          path,
          menuItem.desc
        );
      } else if (String(path).endsWith("csv")) {
        await CsvHandler.export(data, columns, path, grpName);
      } else if (String(path).endsWith("xml")) {
        await XmlHandler.export(data, columns, path, grpName);
      }
      logger.info(`[ParamService] ${t("logs.paramService.exportParamComplete")}`);
      return true;
    } catch (error) {
      logger.error(`[ParamService] ${t("logs.paramService.exportParamError")}`, error);
      return false;
    }
  }

  /**
   * 获取分组参数项（支持分页和全量）
   * @param req 请求体，包含分组名、分页参数等
   * @param all 是否全量获取
   * @returns { data: ParamItem[]; total: number }
   */
  private async getGroupItems(req: IECReq<any>, all: boolean) {
    logger.info(
      `[ParamService] ${t("logs.paramService.getGroupItemsStart")}`,
      req.data.grpName
    );
    const { grpName } = req.data;
    const devInfo = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    const client = devInfo?.deviceClient;

    const menu = devInfo?.debugInfo?.menus;

    let items: DebugInfoItem[] = [];
    if (Array.isArray(grpName)) {
      const settingsMenuItem: DebugInfoMenu[] = [];
      settingsMenuItem.push(
        ...debugInfoMenuService.getTreeItemByFc([MENU_ITEM.SP], req.head.id)
      );
      const settingsSgMenuItem: DebugInfoMenu[] = [];
      settingsSgMenuItem.push(
        ...debugInfoMenuService.getTreeItemByFc([MENU_ITEM.SG], req.head.id)
      );
      // logger.info("settingsSgMenuItem", settingsSgMenuItem.length);
      for (const menuItem of settingsMenuItem || []) {
        if (
          grpName.includes(MENU_ITEM.ALLSETTING_TABLE) ||
          grpName.includes(menuItem.name)
        ) {
          menuItem.items?.forEach((item) => {
            item.fc = menuItem.fc;
            item.grpName = menuItem.desc;
          });
          // logger.info(menuItem.items?.length);
          items.push(...(menuItem.items || []));
        }
      }
      for (const menuItem of settingsSgMenuItem || []) {
        if (
          grpName.includes(MENU_ITEM.ALLEDITSETTING_TABLE) ||
          grpName.includes(menuItem.name)
        ) {
          menuItem.items?.forEach((item) => {
            item.fc = menuItem.fc;
            item.grpName = menuItem.desc;
          });
          // logger.info(menuItem.items?.length);
          items.push(...(menuItem.items || []));
        }
      }
    } else {
      const menuItem: DebugInfoMenu = this.xmlFileManager.findElementByName(
        menu || [],
        grpName
      );
      menuItem?.items?.forEach((item) => {
        item.fc = menuItem.fc;
        item.grpName = menuItem.desc;
      });
      items = menuItem?.items || [];
    }

    items?.forEach((item, index) => {
      item.index = index + 1;
    });

    let filteredResultData;
    let result;
    if (all) {
      filteredResultData = items;
      result = items;
    } else {
      const { pageNum, pageSize, paramName, paramDesc } = req.data;
      const filteredData = paramName
        ? items?.filter((item) => item.name.includes(paramName))
        : items;
      filteredResultData = paramDesc
        ? filteredData?.filter((item) => item.desc.includes(paramDesc))
        : filteredData;
      filteredResultData.forEach((item, index) => {
        item.index = index + 1;
      });
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      result = filteredResultData?.slice(startIndex, endIndex);
    }

    const requestParam: SgValueReadRequestData[] = [];
    const paramMap = new Map();
    result?.forEach((item) => {
      requestParam.push({
        grp: item.grp,
        inf: item.inf || "",
        name: item.name,
        fc: item.fc,
      });
    });
    // logger.info("requestParam:", requestParam);
    // devInfo?.debugInfo?.menus
    const resultData = await client?.getEditSgValue(
      requestParam,
      IECCONSTANTS.BATCH_COUNT
    );
    // logger.info("resultData:", resultData);
    const resultList: ParamItem[] = [];
    if (resultData?.isSuccess()) {
      const data = resultData.data.value;
      if (data && Array.isArray(data)) {
        data.forEach((item) => {
          // if(item.name.includes("IP地址") || item.name.includes("子网掩码")) {
          //   item.value + IpUtils.numberToIp(item.value);
          // }
          paramMap.set(item.name, item);
        });
      }
    } else {
      logger.error(
        `[ParamService] ${t("logs.paramService.getParamValuesFailed")}`,
        resultData?.msg
      );
      throw new Error(String(resultData?.msg));
    }
    result?.forEach((item) => {
      resultList.push({
        index: item.index,
        paramName: item.name,
        inf: String(item.inf),
        paramDesc: item.desc,
        minValue: this.getRealValue(item.type, item.s_min, item.step),
        maxValue: this.getRealValue(item.type, item.s_max, item.step),
        grp: item.grp,
        value: this.getRealValue(
          item.type,
          paramMap.get(item.name) == undefined
            ? ""
            : paramMap.get(item.name).value,
          item.step
        ),
        step: item.step,
        unit: item.unit,
        fc:
          paramMap.get(item.name) == undefined
            ? ""
            : paramMap.get(item.name).fc,
        type: item.type,
        grpName: item.grpName,
      });
    });
    // logger.info("resultList:", resultList.length);
    const params: { data: ParamItem[]; total: number } = {
      data: [],
      total: 0,
    };
    params.data = resultList;
    params.total = filteredResultData?.length;

    logger.info(
      `[ParamService] ${t("logs.paramService.getGroupItemsComplete")}`,
      resultList.length
    );
    return params;
  }

  /**
   * 获取所有分组参数项（多分组全量）
   * @param req 请求体
   * @returns SheetData[]
   */
  private async getAllGroupItems(req: IECReq<any>) {
    logger.info(`[ParamService] ${t("logs.paramService.getAllGroupItemsStart")}`);
    const devInfo = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    const { grpName } = req.data;
    const client = devInfo?.deviceClient;
    //const settingsMenuItem: DebugInfoMenu = this.xmlFileManager.findElementByName(menu || [], MENU_ITEM.SETTING);
    const settingsMenuItem: DebugInfoMenu[] = [];
    logger.info("grpName", grpName);
    if (grpName.includes(MENU_ITEM.ALLSETTING_TABLE)) {
      settingsMenuItem.push(
        ...debugInfoMenuService.getTreeItemByFc([MENU_ITEM.SP], req.head.id)
      );
    }
    if (grpName.includes(MENU_ITEM.ALLEDITSETTING_TABLE)) {
      settingsMenuItem.push(
        ...debugInfoMenuService.getTreeItemByFc([MENU_ITEM.SG], req.head.id)
      );
    }
    const resultValue: SheetData[] = new Array();
    for (const menuItem of settingsMenuItem || []) {
      // logger.info(menuItem);
      // const grpName = menuItem.name;
      const items = menuItem.items;
      items?.forEach((item, index) => {
        item.index = index + 1;
      });

      const fc = menuItem.fc || "";
      const requestParam: SgValueReadRequestData[] = [];
      const paramMap = new Map();
      items?.forEach((item) => {
        requestParam.push({
          grp: item.grp || "",
          inf: item.inf || "",
          name: item.name,
          fc: fc,
        });
      });
      // logger.info("requestParam:", requestParam);
      // devInfo?.debugInfo?.menus
      const resultData = await client?.getEditSgValue(
        requestParam,
        IECCONSTANTS.BATCH_COUNT
      );
      // logger.info("resultData:", resultData);
      const resultList: ParamItem[] = [];
      if (resultData?.isSuccess()) {
        const data = resultData.data.value;
        if (data && Array.isArray(data)) {
          data.forEach((item) => {
            paramMap.set(item.name, item);
          });
        }
      } else {
        logger.error(
          `[ParamService] ${t("logs.paramService.getAllGroupItemsGetValuesFailed")}`,
          resultData?.msg
        );
        throw new Error(String(resultData?.msg));
      }
      items?.forEach((item) => {
        resultList.push({
          index: item.index || 0,
          paramName: item.name,
          inf: String(item.inf),
          paramDesc: item.desc,
          minValue: this.getRealValue(item.type || "", item.s_min, item.step),
          maxValue: this.getRealValue(item.type || "", item.s_max, item.step),
          grp: item.grp || "",
          value: this.getRealValue(
            item.type || "",
            paramMap.get(item.name) == undefined
              ? ""
              : paramMap.get(item.name).value,
            item.step
          ),
          step: item.step || "",
          unit: item.unit || "",
          fc:
            paramMap.get(item.name) == undefined
              ? ""
              : paramMap.get(item.name).fc,
          type: item.type || "",
        });
      });
      resultValue.push({
        data: resultList || [],
        sheetName: menuItem.desc,
        headers: [],
      });
    }

    // 记录每个分组的参数项数，避免 menuItem/resultList 未定义
    settingsMenuItem.forEach((menuItem) => {
      const items = menuItem.items || [];
      logger.info(
        `[ParamService] ${t("logs.paramService.getAllGroupItemsGroupInfo")}：${menuItem.desc}，${t("logs.paramService.paramItemCount")}：${items.length}`
      );
    });
    return resultValue;
  }

  /**
   * 数字类型参数转换为实际值（如IP地址）
   * @param type 参数类型
   * @param value 参数值
   * @param step 步长
   * @returns string 实际值
   */
  private getRealValue(type: string, value: any, step: any): string {
    const result = type == "net" ? IpUtils.numberToIp(value) : value;
    return formatByStep(result, step);
  }

  /**
   * IP地址转换为数字
   * @param type 参数类型
   * @param value 参数值
   * @returns number|string
   */
  private parseAddrToNum(type: string, value: string) {
    return type == "net" ? IpUtils.ipToNumber(value) : value;
  }

  /**
   * 获取当前运行区
   * @param req 请求体
   * @returns ApiResponse
   */
  async getCurrentRunArea(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info(`[ParamService] ${t("logs.paramService.getCurrentRunAreaStart")}`);
      const devInfo = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = devInfo?.deviceClient;
      const response = await client?.getSGCBValues();
      if (response?.isSuccess()) {
        logger.info(`[ParamService] ${t("logs.paramService.getCurrentRunAreaSuccess")}`);
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          getErrorMessage("SUCCESS"),
          response.data
        );
      }
      logger.error(
        `[ParamService] ${t("logs.paramService.getCurrentRunAreaFailed")}`,
        response?.msg
      );
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(`[ParamService] ${t("logs.paramService.getCurrentRunAreaError")}`, error);
      throw error;
    }
  }

  /**
   * 选择定值区
   * @param req 请求体，包含定值区
   * @returns boolean 是否成功
   */
  async selectRunArea(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info(
        `[ParamService] ${t("logs.paramService.selectRunAreaStart")}`,
        req.data.runArea
      );
      const devInfo = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = devInfo?.deviceClient;
      const { runArea } = req.data;
      if (!runArea) {
        logger.error(
          "[ParamService] selectRunArea - " +
            t("logs.paramService.runAreaEmpty")
        );
        throw new Error(t("logs.paramService.runAreaEmpty"));
      }
      const response = await client?.selectEditSG(runArea);
      if (response?.isSuccess()) {
        logger.info(`[ParamService] ${t("logs.paramService.selectRunAreaSuccess")}`);
        return true;
      }
      logger.error(`[ParamService] ${t("logs.paramService.selectRunAreaFailed")}`, response?.msg);
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(`[ParamService] ${t("logs.paramService.selectRunAreaError")}`, error);
      throw error;
    }
  }
}

ParamService.toString = () => "[class ParamService]";
const paramService = new ParamService();

export { ParamService, paramService };
