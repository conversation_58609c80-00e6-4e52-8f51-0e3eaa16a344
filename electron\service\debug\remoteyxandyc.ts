import { DataValue, DataValueReadRequestData, DataValueRes } from "iec-upadrpc/dist/src/data";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { IECReq } from "../../interface/debug/request";
import { IECResult } from "iec-common/dist/data/iecdata";
import { DebugInfoItem, MenuIdName } from "../../interface/debug/debuginfo";
import { debugInfoMenuService } from "./debuginfomenu";
import { ApiPagination } from "../../data/debug/apiPagenation";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import { ExcelExporter } from "../../utils/excelUtils"; // 引入ExcelExporter
import { Column } from "../../interface/debug/exportTypes";
import IECCONSTANTS from "../../data/debug/iecConstants";

class RemoteYxAndYcService {
  constructor() {}

  // 异步获取装置参数定值，返回ParamItem列表
  async ykycGetGroupData(
    req: IECReq<any>
  ): Promise<ApiPagination<DebugInfoItem>> {
    logger.info(
      `[RemoteYxAndYcService] ${t("logs.remoteYxAndYcService.ykycGetGroupDataEntry")}:`,
      JSON.stringify(req)
    );
    try {
      const { pageNum, pageSize } = req.data;
      const resultList = await this.ykycGetAllGroupData(req, false);
      const apiPagination = new ApiPagination<DebugInfoItem>(
        resultList.total,
        pageSize,
        pageNum,
        resultList.data
      );
      logger.info(
        `[RemoteYxAndYcService] ${t("logs.remoteYxAndYcService.ykycGetGroupDataReturn")}:`,
        apiPagination.total
      );
      return apiPagination;
    } catch (error) {
      logger.error(
        `[RemoteYxAndYcService] ${t("logs.remoteYxAndYcService.ykycGetGroupDataError")}:`,
        error
      );
      throw error;
    }
  }

  // 遥测或者遥信获取值
  async ykycGetAllGroupData(req: IECReq<any>, all: boolean) {
    logger.info(
      `[RemoteYxAndYcService] ${t("logs.remoteYxAndYcService.ykycGetAllGroupDataEntry")}`,
      JSON.stringify(req)
    );
    try {
      const { names } = req.data;
      const id = req.head.id;
      const singleGlobalDeviceInfo =
        GlobalDeviceData.getInstance().deviceInfoMap.get(id);
      const client = singleGlobalDeviceInfo?.deviceClient;
      let msg;
      if (client && client.isConnected()) {
        let filteredResultData;
        let result;
        const param: MenuIdName = {
          id: id,
          type: "submenu",
          names: [names[0], names[1]],
        };
        logger.info("param：", param);
        const items: DebugInfoItem[] =
          debugInfoMenuService.getTreeItemByName(param);
        // logger.info("items============：", items);
        // 先过滤数据
        items?.forEach((item, index) => {
          item.index = index + 1;
        });
        const { value } = req.data;
        if (all) {
          filteredResultData = items;
          result = items;
        } else if (value) {
          // 按照值搜索
          const { pageNum, pageSize, name, desc } = req.data;
          logger.info(pageNum, pageSize);
          const filteredData = name
            ? items?.filter((item) => item.name.includes(name))
            : items;
          filteredResultData = desc
            ? filteredData?.filter((item) => item.desc.includes(desc))
            : filteredData;
          result = filteredResultData;
        } else {
          const { pageNum, pageSize, name, desc } = req.data;
          logger.info(pageNum, pageSize);
          const filteredData = name
            ? items?.filter((item) => item.name.includes(name))
            : items;
          filteredResultData = desc
            ? filteredData?.filter((item) => item.desc.includes(desc))
            : filteredData;
          filteredResultData.forEach((items, index) => {
            items.index = index + 1;
          });
          const startIndex = (pageNum - 1) * pageSize;
          const endIndex = startIndex + pageSize;
          result = filteredResultData?.slice(startIndex, endIndex);
        }
        logger.info("result1:", result.length);
        const vkeys = result.map((item) => (item.vKey ? item.vKey : ""));
        const dataRequest: DataValueReadRequestData = {
          infItems: vkeys,
          batchSize: IECCONSTANTS.BATCH_COUNT,
        };
        // logger.info("dataRequest:", dataRequest);
        const resultData: IECResult<DataValueRes> =
          await client.getDataValues(dataRequest);

        const qkeys = result.map((item) => (item.qKey ? item.qKey : ""));
        const dataQRequest: DataValueReadRequestData = {
          infItems: qkeys,
          batchSize: IECCONSTANTS.BATCH_COUNT,
        };
        // logger.info("dataRequest:", dataQRequest);
        const resultQData: IECResult<DataValueRes> =
          await client.getDataValues(dataQRequest);
        logger.info("result.length:", result.length);
        if (resultData.isSuccess() && resultQData.isSuccess()) {
          // logger.info("resultData.data:", resultData.data);
          const vkeyDataValueIte: DataValue[] = Array.isArray(
            resultData.data.value
          )
            ? resultData.data.value
            : [];
          logger.info(
            t("common.requestDataSize"),
            result.length,
            t("common.responseDataSize"),
            vkeyDataValueIte.length,
            "\n"
          );
          if (result.length != vkeyDataValueIte.length) {
            msg = t("errors.vkeysDataLengthMismatch");
            throw new Error(msg);
          }

          const qkeyDataValueIte: DataValue[] = Array.isArray(
            resultQData.data.value
          )
            ? resultQData.data.value
            : [];
          logger.info(
            t("common.requestDataSize"),
            result.length,
            t("common.responseDataSize"),
            qkeyDataValueIte.length,
            "\n"
          );
          if (result.length != qkeyDataValueIte.length) {
            msg = t("errors.qkeysDataLengthMismatch");
            throw new Error(msg);
          }

          for (let i = 0; i < result.length; i++) {
            // logger.info(items[i]);
            result[i].value = vkeyDataValueIte[i]?.value;
            result[i].quality = qkeyDataValueIte[i]?.value || "-";
          }

          if (value) {
            logger.info("######################", value);
            const { pageNum, pageSize } = req.data;
            const selectResult = result.filter((item) => item.value == value);
            selectResult.forEach((items, index) => {
              items.index = index + 1;
            });
            filteredResultData = selectResult;
            const startIndex = (pageNum - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            result = filteredResultData?.slice(startIndex, endIndex);

            logger.info(result[0]);
            logger.info(result.length);
          }
          const params: { data: DebugInfoItem[]; total: number } = {
            data: result,
            total: 0,
          };
          params.total = filteredResultData?.length;
          return params;
        }
        msg = resultData.msg;
      }
      throw new Error(msg);
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }

  // 异步导出参数定值，成功返回true，失败返回false
  async exportAllVal(req: IECReq<any>): Promise<boolean> {
    try {
      const { grpName } = req.data;
      const result = await this.ykycGetAllGroupData(req, true);
      const data: DebugInfoItem[] = result.data;
      const columns: Column[] = [
        { header: t("remoteYxAndYc.headers.index"), key: "index", width: 10 },
        { header: t("remoteYxAndYc.headers.name"), key: "name", width: 20 },
        {
          header: t("remoteYxAndYc.headers.description"),
          key: "desc",
          width: 30,
        },
        { header: t("remoteYxAndYc.headers.value"), key: "value", width: 15 },
        {
          header: t("remoteYxAndYc.headers.quality"),
          key: "quality",
          width: 10,
        },
      ];

      const { path } = req.data;
      const excelExporter = new ExcelExporter();
      await excelExporter.exportToExcel(data, columns, path, grpName);
      return true;
    } catch (error) {
      logger.error(
        `[RemoteYxAndYcService] ${t("logs.remoteYxAndYcService.exportAllValError")}`,
        error
      );
      return false;
    }
  }
}

RemoteYxAndYcService.toString = () => "[class RemoteYxAndYcService]";
const remoteYxAndYcService = new RemoteYxAndYcService();

export { RemoteYxAndYcService, remoteYxAndYcService };
